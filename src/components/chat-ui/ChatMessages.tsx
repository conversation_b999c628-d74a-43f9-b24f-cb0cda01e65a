"use client"

import { Ava<PERSON>, AvatarFallback } from "@/components/ui/avatar"
import { cn, generateStableId } from "@/lib/utils"
import type { ChatMessagesProps } from "@/types/chat"
import { useEffect, useState } from "react"
import { ChatBubble } from "./ChatBubble"

export function ChatMessages({ messages, status }: ChatMessagesProps) {
	const [isClient, setIsClient] = useState(false)

	// Set isClient to true once component mounts
	useEffect(() => {
		setIsClient(true)
	}, [])

	return (
		<div className="space-y-4">
			{messages.map((message) => (
				<ChatBubble
					key={message.id || generateStableId("msg")}
					message={message}
				/>
			))}

			{isClient && status === "thinking" && (
				<div className="flex items-start gap-2">
					<Avatar className="mt-1">
						<AvatarFallback className="bg-blue-600 text-white">
							AI
						</AvatarFallback>
					</Avatar>
					<div className="ai-thought max-w-[80%] px-4 py-3 rounded-lg bg-background text-muted-foreground text-xs">
						<div className="flex items-center gap-2">
							<div className="typing-indicator">
								<span />
								<span />
								<span />
							</div>
							<span className="text-xs text-muted-foreground/70">
								Thinking...
							</span>
						</div>
					</div>
				</div>
			)}
		</div>
	)
}
